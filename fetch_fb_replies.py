import os
import requests
from dotenv import load_dotenv

load_dotenv()

def get_recent_facebook_posts(group_id, access_token, post_limit=1):
    url = f"https://graph.facebook.com/v22.0/{group_id}/feed"
    params = {
        'access_token': access_token
    }
    response = requests.get(url, params=params)
    response.raise_for_status()
    data = response.json()
    return data.get('data', [])

def get_post_comments(post_id, access_token):
    url = f"https://graph.facebook.com/v22.0/{post_id}/comments"
    params = {
        'access_token': access_token,
        'fields': 'from,message,created_time',
        'limit': 100
    }
    try:
        response = requests.get(url, params=params)
        response.raise_for_status()
        data = response.json()
        return data.get('data', [])
    except requests.exceptions.HTTPError as e:
        print(f"    [!] Could not fetch comments for post {post_id}: {e}")
        return None
    except Exception as e:
        print(f"    [!] Unexpected error for post {post_id}: {e}")
        return None

def main():
    GROUP_ID = os.getenv("GROUP_ID")
    ACCESS_TOKEN = os.getenv("ACCESS_TOKEN")
    if not GROUP_ID or not ACCESS_TOKEN:
        print("Error: GROUP_ID and ACCESS_TOKEN must be set in the .env file.")
        return
    print("Fetching latest 5 posts...")
    posts = get_recent_facebook_posts(GROUP_ID, ACCESS_TOKEN, post_limit=5)
    for post in posts:
        post_id = post.get('id')
        print(f"\nPost ID: {post_id}")
        comments = get_post_comments(post_id, ACCESS_TOKEN)
        if comments is None:
            print("  [!] Comments could not be retrieved (permissions or privacy issue).")
        elif not comments:
            print("  No replies found.")
        else:
            for c in comments:
                user = c.get('from', {}).get('name', 'Unknown')
                message = c.get('message', '')
                created = c.get('created_time', '')
                print(f"  - {user} ({created}): {message}")

if __name__ == "__main__":
    main()
