# Telegram Integration Setup Guide

This guide explains how to set up Telegram integration for the Facebook Posts reader script.

## Prerequisites

1. **Install Dependencies**
   ```bash
   pip install -r requirements.txt
   ```

## Telegram Bot Setup

### Step 1: Create a Telegram Bot

1. Open Telegram and search for `@BotFather`
2. Start a conversation with BotFather
3. Send `/newbot` command
4. Follow the instructions to create your bot:
   - Choose a name for your bot (e.g., "Facebook Posts Bot")
   - Choose a username for your bot (must end with "bot", e.g., "my_facebook_posts_bot")
5. BotFather will provide you with a **Bot Token** - save this token!

### Step 2: Get Your Channel ID

#### Option A: For Public Channels
- If your channel username is `@mychannel`, your channel ID is `@mychannel`

#### Option B: For Private Channels
1. Add your bot to the channel as an administrator
2. Send a test message to the channel
3. Visit: `https://api.telegram.org/bot<YOUR_BOT_TOKEN>/getUpdates`
4. Look for the `chat` object in the response and note the `id` field
5. The channel ID will be a negative number (e.g., `-1001234567890`)

### Step 3: Configure Environment Variables

Update your `.env` file with the Telegram credentials:

```env
# Existing Facebook credentials
ACCESS_TOKEN="your_facebook_token_here"
GROUP_ID="your_facebook_group_id_here"

# Telegram Bot Configuration
TELEGRAM_BOT_TOKEN="your_bot_token_from_botfather"
TELEGRAM_CHANNEL_ID="your_channel_id_or_username"
```

## How It Works

### Features Added:

1. **Duplicate Prevention**: The script maintains a local file (`published_posts.txt`) that stores the IDs of posts that have already been published to Telegram.

2. **Automatic Publishing**: New Facebook posts are automatically formatted and sent to your Telegram channel.

3. **Error Handling**: The script gracefully handles Telegram API errors and continues processing other posts.

4. **Status Reporting**: Clear console output shows which posts were sent, skipped, or failed.

### File Structure:
- `ReadFBPost.py` - Main script with Telegram integration
- `published_posts.txt` - Auto-generated file storing published post IDs
- `.env` - Environment variables (including Telegram credentials)
- `requirements.txt` - Python dependencies

### Message Format:
Posts sent to Telegram will be formatted as:
```
**New Facebook Post**

[Original Facebook post content]
```

## Usage

Run the script as usual:
```bash
python ReadFBPost.py
```

The script will:
1. Fetch the 2 most recent Facebook posts
2. Check which posts haven't been published to Telegram yet
3. Send new posts to your Telegram channel
4. Update the tracking file with newly published post IDs
5. Display a summary of actions taken

## Troubleshooting

### Common Issues:

1. **"Telegram not configured" warning**
   - Make sure `TELEGRAM_BOT_TOKEN` and `TELEGRAM_CHANNEL_ID` are set in your `.env` file

2. **"Failed to send to Telegram" error**
   - Verify your bot token is correct
   - Ensure your bot has permission to post in the channel
   - Check that the channel ID is correct

3. **"Bot was blocked by the user" error**
   - Make sure the bot is added to the channel as an administrator

4. **Import errors for telegram module**
   - Run `pip install python-telegram-bot` to install the Telegram library

### Testing:
To test the setup, you can temporarily comment out the duplicate check logic and run the script to see if it sends existing posts to Telegram.
