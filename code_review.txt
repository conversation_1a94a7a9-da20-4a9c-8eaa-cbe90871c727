Please implement the following recommendations from Code Review:
Please implement the following recomendation following code reivew:

Implement a Logging System: Replace all print statements with Python's built-in logging module for better control over log levels and output destinations.

Refactor for DRY (Don't Repeat Yourself):

Create generic helper functions for sending API requests to both Telegram and WhatsApp to reduce code duplication.

Consolidate download_video and download_image into a single download_file function.

Configuration Management: Consider centralizing API endpoints, default timeouts, and max file sizes in a dedicated configuration file or dictionary.

Type Hinting: Add type hints to function signatures for improved code clarity, maintainability, and static analysis.

Constants: Define constants for magic strings (e.g., API URLs, file names, Facebook API version) to improve readability and maintainability.

Error Handling Granularity: Catch more specific exceptions where possible instead of generic Exception to provide more targeted error messages or recovery logic.